<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AJAX File Download Demo</title>
    <style>
        *{margin:0;padding:0;box-sizing:border-box}body{font-family:'Segoe UI',Tahoma,Geneva,Verdana,sans-serif;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);min-height:100vh;display:flex;align-items:center;justify-content:center;overflow:hidden}.particles{position:fixed;top:0;left:0;width:100%;height:100%;pointer-events:none;z-index:-1}.particle{position:absolute;width:4px;height:4px;background:rgba(255,255,255,0.3);border-radius:50%;animation:float 6s ease-in-out infinite}@keyframes float{0%,100%{transform:translateY(0px) rotate(0deg);opacity:0.3}50%{transform:translateY(-20px) rotate(180deg);opacity:0.8}}.progress-container{width:400px;padding:40px;background:rgba(255,255,255,0.95);backdrop-filter:blur(20px);border-radius:20px;box-shadow:0 20px 40px rgba(0,0,0,0.1);border:1px solid rgba(255,255,255,0.2);animation:slideIn 0.8s ease-out;position:relative;overflow:hidden}@keyframes slideIn{from{transform:translateY(50px);opacity:0}to{transform:translateY(0);opacity:1}}.progress-container::before{content:'';position:absolute;top:-50%;left:-50%;width:200%;height:200%;background:linear-gradient(45deg,transparent,rgba(255,255,255,0.1),transparent);transform:rotate(45deg);animation:shine 3s ease-in-out infinite}@keyframes shine{0%{transform:translateX(-100%) translateY(-100%) rotate(45deg)}50%{transform:translateX(100%) translateY(100%) rotate(45deg)}100%{transform:translateX(-100%) translateY(-100%) rotate(45deg)}}.title{text-align:center;margin-bottom:30px;color:#333;font-size:24px;font-weight:600;background:linear-gradient(45deg,#667eea,#764ba2);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.progress-bar{width:100%;height:12px;background:linear-gradient(90deg,#f0f0f0,#e0e0e0);border-radius:20px;overflow:hidden;position:relative;box-shadow:inset 0 2px 4px rgba(0,0,0,0.1)}.progress{height:100%;background:linear-gradient(90deg,#ff6b6b,#feca57,#48dbfb,#ff9ff3);background-size:400% 400%;width:0%;border-radius:20px;position:relative;animation:gradientShift 3s ease infinite;transition:width 0.5s cubic-bezier(0.4,0,0.2,1)}@keyframes gradientShift{0%{background-position:0% 50%}50%{background-position:100% 50%}100%{background-position:0% 50%}}.progress::after{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.4),transparent);animation:progressShine 2s ease-in-out infinite}@keyframes progressShine{0%{transform:translateX(-100%)}100%{transform:translateX(100%)}}.stats{margin:25px 0;display:grid;grid-template-columns:1fr 1fr;gap:15px}.stat-item{background:linear-gradient(135deg,#f8f9fa,#e9ecef);padding:15px;border-radius:12px;text-align:center;box-shadow:0 4px 8px rgba(0,0,0,0.05);transition:transform 0.3s ease,box-shadow 0.3s ease}.stat-item:hover{transform:translateY(-2px);box-shadow:0 8px 16px rgba(0,0,0,0.1)}.stat-label{font-size:12px;color:#666;margin-bottom:5px;text-transform:uppercase;letter-spacing:0.5px}.stat-value{font-size:18px;font-weight:700;color:#333;background:linear-gradient(45deg,#667eea,#764ba2);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.progress-text{grid-column:1/-1;font-size:24px;font-weight:800;text-align:center;margin-top:10px}.buttons{display:flex;gap:15px;margin-top:25px}button{flex:1;padding:15px 25px;border:none;border-radius:12px;font-size:16px;font-weight:600;cursor:pointer;transition:all 0.3s cubic-bezier(0.4,0,0.2,1);position:relative;overflow:hidden}.download-btn{background:linear-gradient(135deg,#667eea,#764ba2);color:white;box-shadow:0 8px 16px rgba(102,126,234,0.3)}.download-btn:hover:not(:disabled){transform:translateY(-2px);box-shadow:0 12px 24px rgba(102,126,234,0.4)}.cancel-btn{background:linear-gradient(135deg,#ff6b6b,#ee5a52);color:white;box-shadow:0 8px 16px rgba(255,107,107,0.3)}.cancel-btn:hover:not(:disabled){transform:translateY(-2px);box-shadow:0 12px 24px rgba(255,107,107,0.4)}button:disabled{opacity:0.6;cursor:not-allowed;transform:none!important}.status-message{margin-top:20px;padding:15px;border-radius:12px;text-align:center;font-weight:600;opacity:0;transform:translateY(10px);transition:all 0.3s ease}.status-message.show{opacity:1;transform:translateY(0)}.status-success{background:linear-gradient(135deg,#51cf66,#40c057);color:white}.status-error{background:linear-gradient(135deg,#ff6b6b,#ee5a52);color:white}.status-info{background:linear-gradient(135deg,#339af0,#228be6);color:white}.downloading{animation:pulse 2s ease-in-out infinite}@keyframes pulse{0%{box-shadow:0 0 0 0 rgba(102,126,234,0.7)}70%{box-shadow:0 0 0 20px rgba(102,126,234,0)}100%{box-shadow:0 0 0 0 rgba(102,126,234,0)}}
    </style>
</head>
<body>
    <!-- 背景粒子动画 -->
    <div class="particles" id="particles"></div>

    <div class="progress-container" id="container">
        <h1 class="title">🚀 File Download Center</h1>

        <div class="progress-bar">
            <div class="progress" id="progressBar"></div>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-label">File Size</div>
                <div class="stat-value" id="fileSize">0 MB</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">Download Speed</div>
                <div class="stat-value" id="downloadSpeed">0 MB/s</div>
            </div>
            <div class="stat-item progress-text">
                <div class="stat-label">Progress</div>
                <div class="stat-value" id="progressPercent">0%</div>
            </div>
        </div>

        <div class="buttons">
            <button id="downloadBtn" class="download-btn" onclick="startDownload()">
                📥 Start Download
            </button>
            <button id="cancelBtn" class="cancel-btn" onclick="cancelDownload()" disabled>
                ❌ Cancel
            </button>
        </div>

        <div class="status-message" id="statusMessage"></div>
    </div>

    <script>
        // ================================
        // 全局变量
        // ================================
        let xhr = null;
        let startTime = null;
        let downloadStartTime = null;

        // ================================
        // 埋点分析模块
        // ================================
        const Analytics = {
            track: function(eventName, eventData = {}) {
                const timestamp = new Date().toISOString();
                const eventInfo = {
                    event: eventName,
                    timestamp: timestamp,
                    userAgent: navigator.userAgent,
                    url: window.location.href,
                    ...eventData
                };

                console.log('📊 Analytics Event:', eventInfo);

                // 发送到分析服务
                // fetch('/analytics', {
                //     method: 'POST',
                //     headers: { 'Content-Type': 'application/json' },
                //     body: JSON.stringify(eventInfo)
                // });
            }
        };

        // ================================
        // UI交互模块
        // ================================
        const UI = {
            showStatus: function(message, type = 'info') {
                const statusEl = document.getElementById('statusMessage');
                statusEl.textContent = message;
                statusEl.className = `status-message show status-${type}`;

                setTimeout(() => {
                    statusEl.classList.remove('show');
                }, 3000);
            },

            updateProgress: function(progress, fileSize, speed) {
                document.getElementById('fileSize').textContent = fileSize;
                document.getElementById('downloadSpeed').textContent = speed;
                document.getElementById('progressPercent').textContent = `${progress}%`;
                document.getElementById('progressBar').style.width = `${progress}%`;
            },

            setDownloadingState: function(isDownloading) {
                const container = document.getElementById('container');
                const downloadBtn = document.getElementById('downloadBtn');
                const cancelBtn = document.getElementById('cancelBtn');

                if (isDownloading) {
                    container.classList.add('downloading');
                    downloadBtn.disabled = true;
                    cancelBtn.disabled = false;
                } else {
                    container.classList.remove('downloading');
                    downloadBtn.disabled = false;
                    cancelBtn.disabled = true;
                }
            },

            resetProgress: function() {
                this.updateProgress(0, '0 MB', '0 MB/s');
                document.getElementById('progressBar').style.width = '0%';
            }
        };

        // ================================
        // 文件保存监听模块
        // ================================
        const FileSaveMonitor = {
            monitor: function() {
                let saveActionDetected = false;
                let cancelActionDetected = false;
                const fileName = 'capcut_installer.exe';

                const handleFocusChange = () => {
                    if (!document.hidden && !saveActionDetected && !cancelActionDetected) {
                        setTimeout(() => this.detectSaveAction(saveActionDetected, cancelActionDetected, cleanup), 500);
                    }
                };

                const handleKeyPress = (event) => {
                    if (event.key === 'Escape' && !saveActionDetected) {
                        cancelActionDetected = true;
                        Analytics.track('file_save_cancelled', {
                            fileName: fileName,
                            reason: 'user_pressed_escape'
                        });
                        UI.showStatus('⚠️ File save cancelled', 'info');
                        cleanup();
                    }
                };

                const cleanup = () => {
                    document.removeEventListener('visibilitychange', handleFocusChange);
                    document.removeEventListener('keydown', handleKeyPress);
                };

                document.addEventListener('visibilitychange', handleFocusChange);
                document.addEventListener('keydown', handleKeyPress);

                setTimeout(() => {
                    if (!saveActionDetected && !cancelActionDetected) {
                        Analytics.track('file_save_timeout', {
                            fileName: fileName,
                            reason: 'no_action_detected_within_timeout'
                        });
                        UI.showStatus('⏰ Save action timeout', 'info');
                    }
                    cleanup();
                }, 30000);
            },

            detectSaveAction: function(saveActionDetected, cancelActionDetected, cleanup) {
                const checkDownloadSuccess = () => {
                    if (!cancelActionDetected) {
                        saveActionDetected = true;
                        Analytics.track('file_save_success', {
                            fileName: 'capcut_installer.exe',
                            saveLocation: 'Downloads',
                            detectionMethod: 'user_interaction_pattern'
                        });
                        UI.showStatus('✅ File saved successfully!', 'success');
                        cleanup();
                    }
                };
                setTimeout(checkDownloadSuccess, 2000);
            }
        };

        // ================================
        // 动画效果模块
        // ================================
        const Animation = {
            createParticles: function() {
                const particlesContainer = document.getElementById('particles');
                for (let i = 0; i < 50; i++) {
                    const particle = document.createElement('div');
                    particle.className = 'particle';
                    particle.style.left = Math.random() * 100 + '%';
                    particle.style.top = Math.random() * 100 + '%';
                    particle.style.animationDelay = Math.random() * 6 + 's';
                    particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                    particlesContainer.appendChild(particle);
                }
            }
        };

        // ================================
        // 下载管理模块
        // ================================
        const DownloadManager = {
            config: {
                fileName: 'capcut_installer.exe',
                url: 'https://lf16-capcut.faceulv.com/obj/capcutpc-packages-sg/installer/capcut_capcutpc_0_1.2.6_installer.exe'
            },

            start: function() {
                Analytics.track('download_started', {
                    fileName: this.config.fileName,
                    fileUrl: this.config.url
                });

                UI.setDownloadingState(true);
                UI.resetProgress();
                UI.showStatus('🚀 Starting download...', 'info');

                xhr = new XMLHttpRequest();
                startTime = Date.now();
                downloadStartTime = startTime;
                let lastUpdateTime = startTime;
                let lastLoaded = 0;

                xhr.open('GET', this.config.url, true);
                xhr.responseType = 'blob';

                this.setupProgressHandler(lastUpdateTime, lastLoaded);
                this.setupCompleteHandler();
                this.setupErrorHandler();
                xhr.send();
            },

            setupProgressHandler: function(lastUpdateTime, lastLoaded) {
                xhr.onprogress = (event) => {
                    if (event.lengthComputable) {
                        const loaded = event.loaded;
                        const total = event.total;
                        const currentTime = Date.now();
                        const timeDiff = (currentTime - lastUpdateTime) / 1000;

                        const bytesInInterval = loaded - lastLoaded;
                        const speed = timeDiff > 0 ? (bytesInInterval / (1024 * 1024) / timeDiff).toFixed(2) : 0;

                        lastUpdateTime = currentTime;
                        lastLoaded = loaded;

                        const progress = (loaded / total * 100).toFixed(1);
                        const fileSize = (total / 1024 / 1024).toFixed(2);
                        const downloadedSize = (loaded / 1024 / 1024).toFixed(2);

                        UI.updateProgress(progress, `${downloadedSize}/${fileSize} MB`, `${speed} MB/s`);

                        // 进度埋点
                        const progressInt = Math.floor(progress);
                        if (progressInt > 0 && progressInt % 25 === 0 && !window[`progress_${progressInt}_tracked`]) {
                            Analytics.track('download_progress', {
                                progress: progressInt,
                                downloadSpeed: speed,
                                fileSize: fileSize,
                                elapsedTime: (currentTime - downloadStartTime) / 1000
                            });
                            window[`progress_${progressInt}_tracked`] = true;
                        }
                    }
                };
            },

            setupCompleteHandler: function() {

                xhr.onload = () => {
                    UI.setDownloadingState(false);

                    if (xhr.status === 200) {
                        try {
                            const blob = new Blob([xhr.response], { type: 'application/octet-stream' });
                            const link = document.createElement('a');
                            link.href = window.URL.createObjectURL(blob);
                            link.download = this.config.fileName;

                            link.addEventListener('click', () => {
                                const totalTime = (Date.now() - downloadStartTime) / 1000;
                                Analytics.track('download_completed', {
                                    fileName: this.config.fileName,
                                    totalTime: totalTime,
                                });

                                UI.showStatus('✅ Download completed successfully!', 'success');
                                FileSaveMonitor.monitor();
                            });

                            link.click();

                        } catch (error) {
                            Analytics.track('file_creation_failed', {
                                error: error.message,
                                fileName: this.config.fileName,
                                errorType: 'technical_error'
                            });
                            UI.showStatus('❌ Failed to create download file', 'error');
                        }
                    } else {
                        Analytics.track('download_failed', {
                            statusCode: xhr.status,
                            statusText: xhr.statusText,
                            elapsedTime: (Date.now() - downloadStartTime) / 1000
                        });
                        UI.showStatus('❌ Download failed', 'error');
                    }

                    this.resetProgressTracking();
                };
            },

            setupErrorHandler: function() {

                xhr.onerror = () => {
                    UI.setDownloadingState(false);

                    Analytics.track('download_error', {
                        error: 'Network error',
                        elapsedTime: (Date.now() - downloadStartTime) / 1000
                    });

                    UI.updateProgress('Error', 'Error', 'Error');
                    UI.showStatus('❌ Network error occurred', 'error');
                };
            },

            cancel: function() {
                if (xhr) {
                    Analytics.track('download_cancelled', {
                        elapsedTime: downloadStartTime ? (Date.now() - downloadStartTime) / 1000 : 0,
                        progress: document.getElementById('progressPercent').textContent
                    });

                    xhr.abort();
                    UI.setDownloadingState(false);
                    UI.updateProgress('Cancelled', 'Cancelled', 'Cancelled');
                    UI.resetProgress();
                    UI.showStatus('⏹️ Download cancelled', 'info');

                    this.resetProgressTracking();
                }
            },

            resetProgressTracking: function() {
                for (let i = 25; i <= 100; i += 25) {
                    delete window[`progress_${i}_tracked`];
                }
            }
        };

        // ================================
        // 主函数 - 供HTML调用
        // ================================
        function startDownload() {
            DownloadManager.start();
        }

        function cancelDownload() {
            DownloadManager.cancel();
        }

        // ================================
        // 初始化
        // ================================
        document.addEventListener('DOMContentLoaded', function() {
            Animation.createParticles();

            Analytics.track('page_loaded', {
                loadTime: performance.now()
            });
        });
    </script>
</body>
</html>
